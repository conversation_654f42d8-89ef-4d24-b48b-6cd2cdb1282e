#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
创建production_lines表并插入测试数据
"""

import pymysql
import logging

# 数据库配置
MALFUNCTION_DB_CONFIG = {
    'host': '*************',
    'database': 'production_data',
    'user': 'root',
    'password': '20016f7d55f95fb9',
    'charset': 'utf8mb4',
    'autocommit': True
}

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_table():
    """创建production_lines表"""
    connection = None
    try:
        connection = pymysql.connect(**MALFUNCTION_DB_CONFIG)
        
        with connection.cursor() as cursor:
            # 创建表的SQL
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS `production_lines` (
              `line_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '生产线ID',
              `line_number` int(11) NOT NULL COMMENT '生产线编号',
              `product_id` varchar(255) NOT NULL COMMENT '产品号',
              `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
              `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
              PRIMARY KEY (`line_id`),
              UNIQUE KEY `line_number` (`line_number`)
            ) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='生产线基础信息表';
            """
            
            cursor.execute(create_table_sql)
            logger.info("production_lines表创建成功")
            
    except Exception as e:
        logger.error(f"创建表失败: {e}")
        return False
    finally:
        if connection:
            connection.close()
    
    return True

def insert_test_data():
    """插入测试数据"""
    connection = None
    try:
        connection = pymysql.connect(**MALFUNCTION_DB_CONFIG)
        
        with connection.cursor() as cursor:
            # 测试数据
            test_data = [
                (1, "35-0050-3"),
                (2, "35-0051-3"),
                (3, "35-0052-3"),
                (4, "35-0053-3"),
                (5, "35-0054-3"),
            ]
            
            # 插入数据的SQL
            insert_sql = """
            INSERT INTO production_lines (line_number, product_id) 
            VALUES (%s, %s) 
            ON DUPLICATE KEY UPDATE product_id = VALUES(product_id)
            """
            
            for line_number, product_id in test_data:
                cursor.execute(insert_sql, (line_number, product_id))
                logger.info(f"插入数据: line_number={line_number}, product_id={product_id}")
            
            logger.info("测试数据插入成功")
            
    except Exception as e:
        logger.error(f"插入数据失败: {e}")
        return False
    finally:
        if connection:
            connection.close()
    
    return True

def verify_data():
    """验证数据"""
    connection = None
    try:
        connection = pymysql.connect(**MALFUNCTION_DB_CONFIG)
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT line_number, product_id FROM production_lines ORDER BY line_number")
            results = cursor.fetchall()
            
            logger.info("当前数据库中的数据:")
            for line_number, product_id in results:
                logger.info(f"  生产线编号: {line_number}, 产品号: {product_id}")
                
    except Exception as e:
        logger.error(f"验证数据失败: {e}")
        return False
    finally:
        if connection:
            connection.close()
    
    return True

def main():
    """主函数"""
    logger.info("开始初始化数据库...")
    
    # 创建表
    if not create_table():
        logger.error("创建表失败，退出")
        return
    
    # 插入测试数据
    if not insert_test_data():
        logger.error("插入测试数据失败，退出")
        return
    
    # 验证数据
    if not verify_data():
        logger.error("验证数据失败")
        return
    
    logger.info("数据库初始化完成!")

if __name__ == '__main__':
    main()
