#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产品号查询API
根据line参数返回对应的产品号
"""

from flask import Flask, request, jsonify

app = Flask(__name__)

# 产品号映射表
PRODUCT_MAPPING = {
    1: "35-0050-3",
    # 可以根据需要添加更多映射
    # 2: "35-0051-3",
    # 3: "35-0052-3",
}

@app.route('/product', methods=['POST'])
def get_product():
    """
    获取产品号
    请求参数: {"line": 1}
    返回: {"product_number": "35-0050-3"}
    """
    try:
        # 获取JSON数据
        data = request.get_json()
        
        if not data:
            return jsonify({"error": "请求体不能为空"}), 400
        
        # 获取line参数
        line = data.get('line')
        
        if line is None:
            return jsonify({"error": "缺少line参数"}), 400
        
        # 检查line是否为整数
        if not isinstance(line, int):
            return jsonify({"error": "line参数必须为整数"}), 400
        
        # 查找对应的产品号
        product_number = PRODUCT_MAPPING.get(line)
        
        if product_number is None:
            return jsonify({"error": f"未找到line={line}对应的产品号"}), 404
        
        return jsonify({"product_number": product_number})
        
    except Exception as e:
        return jsonify({"error": f"服务器内部错误: {str(e)}"}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({"status": "ok"})

if __name__ == '__main__':
    print("启动产品号查询API服务...")
    print("请求示例: POST /product")
    print("请求体: {\"line\": 1}")
    print("返回: {\"product_number\": \"35-0050-3\"}")
    app.run(host='0.0.0.0', port=5001, debug=True)
