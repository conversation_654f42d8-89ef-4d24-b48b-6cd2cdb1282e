#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产品号查询API
根据line参数从数据库查询对应的产品号
"""

from flask import Flask, request, jsonify
import pymysql
import logging

app = Flask(__name__)

# 数据库配置
MALFUNCTION_DB_CONFIG = {
    'host': '*************',
    'database': 'production_data',
    'user': 'root',
    'password': '20016f7d55f95fb9',
    'charset': 'utf8mb4',
    'autocommit': True
}

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = pymysql.connect(**MALFUNCTION_DB_CONFIG)
        return connection
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def get_product_by_line(line_number):
    """根据生产线编号查询产品号"""
    connection = get_db_connection()
    if not connection:
        return None, "数据库连接失败"

    try:
        with connection.cursor() as cursor:
            sql = "SELECT product_id FROM production_lines WHERE line_number = %s"
            cursor.execute(sql, (line_number,))
            result = cursor.fetchone()

            if result:
                return result[0], None
            else:
                return None, f"未找到生产线编号 {line_number} 对应的产品号"

    except Exception as e:
        logger.error(f"查询数据库失败: {e}")
        return None, f"查询失败: {str(e)}"
    finally:
        connection.close()

@app.route('/product', methods=['POST'])
def get_product():
    """
    获取产品号
    请求参数: {"line": 1}
    返回: {"product_number": "35-0050-3"}
    """
    try:
        # 获取JSON数据
        data = request.get_json()
        
        if not data:
            return jsonify({"error": "请求体不能为空"}), 400
        
        # 获取line参数
        line = data.get('line')
        
        if line is None:
            return jsonify({"error": "缺少line参数"}), 400
        
        # 检查line是否为整数
        if not isinstance(line, int):
            return jsonify({"error": "line参数必须为整数"}), 400
        
        # 从数据库查找对应的产品号
        product_number, error = get_product_by_line(line)

        if error:
            return jsonify({"error": error}), 404

        return jsonify({"product_number": product_number})
        
    except Exception as e:
        return jsonify({"error": f"服务器内部错误: {str(e)}"}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({"status": "ok"})

if __name__ == '__main__':
    print("启动产品号查询API服务...")
    print("请求示例: POST /product")
    print("请求体: {\"line\": 1}")
    print("返回: {\"product_number\": \"35-0050-3\"}")
    app.run(host='0.0.0.0', port=5001, debug=True)
