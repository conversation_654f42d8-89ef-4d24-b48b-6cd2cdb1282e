#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产品号查询API测试脚本
"""

import requests
import json

def test_product_api():
    """测试产品号查询API"""
    base_url = "http://localhost:5001"
    
    # 测试用例1: 正常请求
    print("测试用例1: 正常请求 line=1")
    try:
        response = requests.post(
            f"{base_url}/product",
            json={"line": 1},
            headers={"Content-Type": "application/json"}
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        print()
    except Exception as e:
        print(f"请求失败: {e}")
        print()
    
    # 测试用例2: 缺少line参数
    print("测试用例2: 缺少line参数")
    try:
        response = requests.post(
            f"{base_url}/product",
            json={},
            headers={"Content-Type": "application/json"}
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        print()
    except Exception as e:
        print(f"请求失败: {e}")
        print()
    
    # 测试用例3: line参数不存在对应产品号
    print("测试用例3: line=999 (不存在)")
    try:
        response = requests.post(
            f"{base_url}/product",
            json={"line": 999},
            headers={"Content-Type": "application/json"}
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        print()
    except Exception as e:
        print(f"请求失败: {e}")
        print()
    
    # 测试用例4: 健康检查
    print("测试用例4: 健康检查")
    try:
        response = requests.get(f"{base_url}/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        print()
    except Exception as e:
        print(f"请求失败: {e}")
        print()

if __name__ == '__main__':
    print("开始测试产品号查询API...")
    print("请确保API服务已启动 (python product_api.py)")
    print("=" * 50)
    test_product_api()
