#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产品号更新接口测试脚本
"""

import requests
import json

def test_update_product():
    """测试产品号更新接口"""
    base_url = "http://localhost:5001"
    
    print("=" * 60)
    print("产品号更新接口测试")
    print("=" * 60)
    
    # 测试1: 查看当前line=1的产品号
    print("1. 查看当前line=1的产品号")
    try:
        response = requests.post(
            f"{base_url}/product",
            json={"line": 1},
            headers={"Content-Type": "application/json"}
        )
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            current_product = response.json().get("product_number")
            print(f"   当前产品号: {current_product}")
        else:
            print(f"   响应: {response.json()}")
        print()
    except Exception as e:
        print(f"   请求失败: {e}")
        print()
    
    # 测试2: 更新line=1的产品号
    new_product_id = "35-0099-3"
    print(f"2. 更新line=1的产品号为: {new_product_id}")
    try:
        response = requests.post(
            f"{base_url}/product/update",
            json={"line": 1, "product_id": new_product_id},
            headers={"Content-Type": "application/json"}
        )
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")
        print()
    except Exception as e:
        print(f"   请求失败: {e}")
        print()
    
    # 测试3: 验证更新结果
    print("3. 验证更新结果")
    try:
        response = requests.post(
            f"{base_url}/product",
            json={"line": 1},
            headers={"Content-Type": "application/json"}
        )
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            updated_product = response.json().get("product_number")
            print(f"   更新后产品号: {updated_product}")
            if updated_product == new_product_id:
                print("   ✅ 更新成功!")
            else:
                print("   ❌ 更新失败!")
        else:
            print(f"   响应: {response.json()}")
        print()
    except Exception as e:
        print(f"   请求失败: {e}")
        print()
    
    # 测试4: 新增一个不存在的生产线
    print("4. 新增生产线line=99")
    try:
        response = requests.post(
            f"{base_url}/product/update",
            json={"line": 99, "product_id": "35-0199-3"},
            headers={"Content-Type": "application/json"}
        )
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")
        print()
    except Exception as e:
        print(f"   请求失败: {e}")
        print()
    
    # 测试5: 验证新增的生产线
    print("5. 验证新增的生产线line=99")
    try:
        response = requests.post(
            f"{base_url}/product",
            json={"line": 99},
            headers={"Content-Type": "application/json"}
        )
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")
        print()
    except Exception as e:
        print(f"   请求失败: {e}")
        print()
    
    # 测试6: 错误测试 - 缺少product_id
    print("6. 错误测试 - 缺少product_id参数")
    try:
        response = requests.post(
            f"{base_url}/product/update",
            json={"line": 1},
            headers={"Content-Type": "application/json"}
        )
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")
        print()
    except Exception as e:
        print(f"   请求失败: {e}")
        print()
    
    # 测试7: 错误测试 - 缺少line参数
    print("7. 错误测试 - 缺少line参数")
    try:
        response = requests.post(
            f"{base_url}/product/update",
            json={"product_id": "35-0100-3"},
            headers={"Content-Type": "application/json"}
        )
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")
        print()
    except Exception as e:
        print(f"   请求失败: {e}")
        print()
    
    print("=" * 60)
    print("测试完成!")
    print("=" * 60)

if __name__ == '__main__':
    print("产品号更新接口测试脚本")
    print("请确保API服务已启动 (python product_api.py)")
    print()
    test_update_product()
